package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"os"
	"veracrypt-go/shared"
)

// CreateEmptyContainer đ<PERSON><PERSON><PERSON> cập nhật để tạo và lưu salt, và khởi tạo FAT.
func CreateEmptyContainer(filePath string, sizeMB int, password string) error {
	totalSizeBytes := int64(sizeMB) * 1024 * 1024
	totalBlocks := uint32(totalSizeBytes / shared.BLOCK_SIZE)
	fatSizeInBytes := int64(shared.MAX_FILES * binary.Size(shared.FileAllocationEntry{}))
	// Mỗi block chỉ có thể chứa (BLOCK_SIZE - 16) bytes dữ liệu thực
	maxDataPerBlock := shared.BLOCK_SIZE - 16
	fatBlocks := uint32((fatSizeInBytes + int64(maxDataPerBlock) - 1) / int64(maxDataPerBlock))
	if totalBlocks <= fatBlocks+1 {
		return fmt.Errorf("container size is too small for metadata")
	}

	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	if err := file.Truncate(totalSizeBytes); err != nil {
		return err
	}

	// TẠO SALT NGẪU NHIÊN
	salt, err := generateSalt()
	if err != nil {
		return fmt.Errorf("failed to generate salt: %v", err)
	}
	var salt32 [32]byte
	copy(salt32[:], salt)

	superblock := shared.Superblock{
		MagicNumber: [4]byte{'V', 'C', 'F', 'S'},
		Version:     1,
		Salt:        salt32, // Lưu salt vào superblock
		TotalBlocks: totalBlocks,
		FatBlocks:   fatBlocks,
	}

	// Ghi superblock (không mã hóa)
	var sbBuffer bytes.Buffer
	if err := binary.Write(&sbBuffer, binary.LittleEndian, &superblock); err != nil {
		return fmt.Errorf("failed to serialize superblock: %v", err)
	}
	if _, err := file.WriteAt(sbBuffer.Bytes(), 0); err != nil {
		return fmt.Errorf("failed to write superblock: %v", err)
	}

	// Tạo crypter để mã hóa FAT
	crypter, err := NewBlockCrypter(password, salt32[:])
	if err != nil {
		return fmt.Errorf("failed to create crypter: %v", err)
	}

	// Khởi tạo FAT trống (tất cả entries đều không sử dụng)
	emptyFAT := make([]shared.FileAllocationEntry, shared.MAX_FILES)
	for i := range emptyFAT {
		emptyFAT[i].InUse = false
	}

	// Ghi FAT đã mã hóa vào các block
	if err := WriteFAT(file, &superblock, emptyFAT, crypter); err != nil {
		return fmt.Errorf("failed to write initial FAT: %v", err)
	}

	fmt.Printf("✅ Container created successfully: %s (%dMB)\n", filePath, sizeMB)
	fmt.Printf("   Total blocks: %d, FAT blocks: %d\n", totalBlocks, fatBlocks)
	return nil
}

// readBlock được cập nhật để nhận vào một crypter và GIẢI MÃ.
func readBlock(file *os.File, blockIndex uint32, crypter *BlockCrypter) ([]byte, error) {
	encryptedBuffer := make([]byte, shared.BLOCK_SIZE)
	offset := int64(blockIndex) * shared.BLOCK_SIZE

	bytesRead, err := file.ReadAt(encryptedBuffer, offset)
	if err != nil {
		return nil, err
	}
	if bytesRead != shared.BLOCK_SIZE {
		return nil, fmt.Errorf("short read: expected %d bytes, got %d", shared.BLOCK_SIZE, bytesRead)
	}

	// GIẢI MÃ dữ liệu ngay sau khi đọc
	decryptedData, err := crypter.DecryptBlock(encryptedBuffer, blockIndex)
	if err != nil {
		return nil, err
	}

	return decryptedData, nil
}

// writeBlock được cập nhật để nhận vào một crypter và MÃ HÓA.
func writeBlock(file *os.File, blockIndex uint32, plaintextData []byte, crypter *BlockCrypter) error {
	// Kích thước plaintext tối đa phải để chỗ cho GCM tag (16 bytes)
	maxPlaintextSize := shared.BLOCK_SIZE - 16
	if len(plaintextData) > maxPlaintextSize {
		return fmt.Errorf("data size (%d) is larger than max plaintext size (%d)", len(plaintextData), maxPlaintextSize)
	}

	// Đệm dữ liệu thô đến kích thước tối đa cho phép
	if len(plaintextData) < maxPlaintextSize {
		paddedData := make([]byte, maxPlaintextSize)
		copy(paddedData, plaintextData)
		plaintextData = paddedData
	}

	// MÃ HÓA dữ liệu trước khi ghi (sẽ tạo ra đúng BLOCK_SIZE bytes)
	encryptedData := crypter.EncryptBlock(plaintextData, blockIndex)

	// Kiểm tra kích thước ciphertext
	if len(encryptedData) != shared.BLOCK_SIZE {
		return fmt.Errorf("encrypted data size (%d) does not match block size (%d)", len(encryptedData), shared.BLOCK_SIZE)
	}

	// Ghi dữ liệu đã mã hóa xuống đĩa
	offset := int64(blockIndex) * shared.BLOCK_SIZE
	_, err := file.WriteAt(encryptedData, offset)
	if err != nil {
		return err
	}

	return nil
}

// Hàm tiện ích để đọc Superblock
func readSuperblock(file *os.File) (*shared.Superblock, error) {
	buffer := make([]byte, shared.BLOCK_SIZE)
	if _, err := file.ReadAt(buffer, 0); err != nil {
		return nil, err
	}

	var sb shared.Superblock
	reader := bytes.NewReader(buffer)
	if err := binary.Read(reader, binary.LittleEndian, &sb); err != nil {
		return nil, err
	}

	return &sb, nil
}
