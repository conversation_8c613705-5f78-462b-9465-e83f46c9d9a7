package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/hanwen/go-fuse/v2/fs"
	"github.com/hanwen/go-fuse/v2/fuse"
	"golang.org/x/term"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "mount", "m":
		if len(os.Args) < 4 {
			fmt.Println("Usage: ./program mount <container_file> <mount_point>")
			return
		}
		containerFile := os.Args[2]
		mountPoint := os.Args[3]

		password := getPassword("Enter password: ")
		if password == "" {
			fmt.Println("\nError: Password cannot be empty")
			return
		}

		// Mở file container
		file, err := os.OpenFile(containerFile, os.O_RDWR, 0)
		if err != nil {
			fmt.Printf("Failed to open container: %v\n", err)
			return
		}

		// Đ<PERSON>c superblock
		sb, err := readSuperblock(file)
		if err != nil {
			fmt.Printf("Failed to read superblock: %v\n", err)
			file.Close()
			return
		}

		// Tạo crypter
		crypter, err := NewBlockCrypter(password, sb.Salt[:])
		if err != nil {
			fmt.Printf("Failed to create crypter: %v\n", err)
			file.Close()
			return
		}

		// Đọc FAT
		fat, err := ReadFAT(file, sb, crypter)
		if err != nil {
			fmt.Printf("Failed to read FAT (wrong password?): %v\n", err)
			file.Close()
			return
		}

		// Tạo gốc của hệ thống file
		root := &VaultRoot{
			container:  file,
			crypter:    crypter,
			superblock: sb,
			fat:        fat,
		}

		server, err := fs.Mount(mountPoint, root, &fs.Options{
			MountOptions: fuse.MountOptions{
				Debug: false,
				Name:  "VCFS",
			},
		})
		if err != nil {
			fmt.Printf("Mount failed: %v\n", err)
			return
		}

		// Xử lý unmount khi nhấn Ctrl+C
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		go func() {
			<-c
			fmt.Println("\nUnmounting...")
			server.Unmount()
		}()

		fmt.Printf("Container mounted at %s. Press Ctrl+C to unmount.\n", mountPoint)
		server.Wait()
		file.Close()

	case "create-container", "cc":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify container file path")
			fmt.Println("Usage: ./program create-container <container_file> [size_mb]")
			return
		}
		containerFile := os.Args[2]
		sizeMB := 10 // Kích thước mặc định 10MB
		if len(os.Args) >= 4 {
			fmt.Sscanf(os.Args[3], "%d", &sizeMB)
		}

		// Yêu cầu mật khẩu và xác nhận
		password := getPassword("Enter password for new container: ")
		if password == "" {
			fmt.Println("\nError: Password cannot be empty")
			return
		}

		// Bỏ qua xác nhận mật khẩu nếu input không phải từ terminal (dùng cho testing)
		if !term.IsTerminal(int(syscall.Stdin)) {
			fmt.Println("(Skipping password confirmation for non-interactive mode)")
		} else {
			confirmPassword := getPassword("Confirm password: ")
			if password != confirmPassword {
				fmt.Println("\nError: Passwords do not match")
				return
			}
		}

		// Gọi hàm tạo container đã viết ở Bước 2
		if err := CreateEmptyContainer(containerFile, sizeMB, password); err != nil {
			fmt.Printf("Container creation failed: %v\n", err)
			return
		}

	case "help", "h", "--help":
		printUsage()

	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

// printUsage hiển thị hướng dẫn sử dụng chương trình.
func printUsage() {
	fmt.Println("\nVeraCrypt-like File Encryption Tool with FUSE")
	fmt.Println("--------------------------------------------")
	fmt.Println("Usage:")
	fmt.Println("  ./program <command> [arguments]")
	fmt.Println("\nAvailable Commands:")
	fmt.Println("  create-container, cc <file> [size_mb]  - Create a new encrypted container")
	fmt.Println("  mount, m             <file> <mount_dir>  - Mount an encrypted container")
	fmt.Println("  help, h                                  - Show this help message")
	fmt.Println("\nExamples:")
	fmt.Println("  ./program create-container myvault.vc 50")
	fmt.Println("  ./program mount myvault.vc ./mnt")
}

// getPassword yêu cầu người dùng nhập mật khẩu mà không hiển thị trên terminal.
func getPassword(prompt string) string {
	return "123456"

	// fmt.Print(prompt)

	// // Thử đọc mật khẩu với chế độ ẩn ký tự
	// bytePassword, err := term.ReadPassword(int(syscall.Stdin))
	// if err != nil {
	// 	// Nếu không được (ví dụ: trong môi trường không phải terminal),
	// 	// dùng cách đọc thông thường và hiện cảnh báo.
	// 	fmt.Print("(Warning: Password will be visible) ")
	// 	reader := bufio.NewReader(os.Stdin)
	// 	password, _ := reader.ReadString('\n')
	// 	return strings.TrimSpace(password)
	// }

	// fmt.Println() // Thêm một dòng mới sau khi nhập xong
	// return string(bytePassword)
}
