package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/binary"
	"fmt"

	// Sử dụng lại các hằng số
	"golang.org/x/crypto/pbkdf2"
)

const (
	KEY_SIZE   = 32     // AES-256
	NONCE_SIZE = 12     // Kích thước <PERSON>ce tiêu chuẩn cho GCM
	TAG_SIZE   = 16     // Kích thước GCM authentication tag
	ITERATIONS = 100000 // Số vòng lặp PBKDF2
)

// BlockCrypter xử lý việc mã hóa và giải mã cho từng block.
type BlockCrypter struct {
	key []byte
	gcm cipher.AEAD
}

// NewBlockCrypter tạo một thực thể BlockCrypter mới từ password và salt.
func NewBlockCrypter(password string, salt []byte) (*BlockCrypter, error) {
	// 1. Tạo key từ password và salt
	key := pbkdf2.Key([]byte(password), salt, ITERATIONS, KEY_SIZE, sha256.New)

	// 2. <PERSON><PERSON><PERSON> bị bộ mã hóa AES
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %v", err)
	}

	// 3. Chuẩn bị chế độ GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %v", err)
	}

	return &BlockCrypter{
		key: key,
		gcm: gcm,
	}, nil
}

// generateNonceForBlock tạo ra một Nonce duy nhất cho một block cụ thể.
func (bc *BlockCrypter) generateNonceForBlock(blockIndex uint32) []byte {
	nonce := make([]byte, NONCE_SIZE)
	// Chúng ta sẽ dùng 8 byte đầu của nonce để lưu blockIndex (dưới dạng uint64).
	// Điều này đảm bảo mỗi block có một nonce khác nhau.
	// 4 byte còn lại có thể để là 0 hoặc một giá trị ngẫu nhiên cố định.
	binary.LittleEndian.PutUint64(nonce, uint64(blockIndex))
	return nonce
}

// EncryptBlock mã hóa một block dữ liệu.
func (bc *BlockCrypter) EncryptBlock(plaintext []byte, blockIndex uint32) []byte {
	nonce := bc.generateNonceForBlock(blockIndex)

	// gcm.Seal sẽ mã hóa và thêm một tag xác thực vào cuối.
	// Vì kích thước ciphertext sẽ lớn hơn plaintext một chút (do có tag),
	// chúng ta cần đảm bảo buffer đầu vào của writeBlock đủ lớn.
	// Tuy nhiên, trong thiết kế của chúng ta, plaintext đã được đệm đủ BLOCK_SIZE,
	// và GCM tag (16 bytes) sẽ nằm trong phần đệm đó.
	// Đây là một sự đơn giản hóa, hệ thống thật sẽ phức tạp hơn.
	// Để an toàn, chúng ta sẽ mã hóa và trả về.
	ciphertext := bc.gcm.Seal(nil, nonce, plaintext, nil)
	return ciphertext
}

// DecryptBlock giải mã một block dữ liệu.
func (bc *BlockCrypter) DecryptBlock(ciphertext []byte, blockIndex uint32) ([]byte, error) {
	nonce := bc.generateNonceForBlock(blockIndex)

	plaintext, err := bc.gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		// Lỗi ở đây có thể do sai password (dẫn đến sai key) hoặc dữ liệu đã bị sửa đổi.
		return nil, fmt.Errorf("failed to decrypt block %d: %v", blockIndex, err)
	}

	return plaintext, nil
}

// generateSalt tạo ra một salt ngẫu nhiên.
func generateSalt() ([]byte, error) {
	salt := make([]byte, 32)
	if _, err := rand.Read(salt); err != nil {
		return nil, err
	}
	return salt, nil
}
