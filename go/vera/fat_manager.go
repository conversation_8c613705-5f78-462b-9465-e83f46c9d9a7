package main

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"os"
	"veracrypt-go/shared"
)

// ReadFAT đọc toàn bộ các block chứa FAT từ container và phân tích chúng.
func ReadFAT(file *os.File, sb *shared.Superblock, crypter *BlockCrypter) ([]shared.FileAllocationEntry, error) {
	// Mỗi block chỉ chứa (BLOCK_SIZE - 16) bytes dữ liệu thực
	maxDataPerBlock := shared.BLOCK_SIZE - 16
	fatData := make([]byte, 0, int(sb.FatBlocks)*maxDataPerBlock)

	// Đọc tuần tự các block của FAT (bắt đầu từ block 1)
	for i := uint32(0); i < sb.FatBlocks; i++ {
		blockIndex := 1 + i // Block 0 là Superblock
		block, err := readBlock(file, blockIndex, crypter)
		if err != nil {
			return nil, fmt.Errorf("failed to read FAT block %d: %v", blockIndex, err)
		}
		fatData = append(fatData, block...)
	}

	// Chuyển mảng byte thành mảng các struct FileAllocationEntry
	fat := make([]shared.FileAllocationEntry, shared.MAX_FILES)
	reader := bytes.NewReader(fatData)
	if err := binary.Read(reader, binary.LittleEndian, &fat); err != nil {
		return nil, fmt.Errorf("failed to deserialize FAT: %v", err)
	}

	return fat, nil
}

// WriteFAT ghi toàn bộ cấu trúc FAT trong bộ nhớ vào các block trên đĩa.
func WriteFAT(file *os.File, sb *shared.Superblock, fat []shared.FileAllocationEntry, crypter *BlockCrypter) error {
	// Chuyển mảng struct thành một mảng byte lớn
	var fatBuffer bytes.Buffer
	if err := binary.Write(&fatBuffer, binary.LittleEndian, &fat); err != nil {
		return fmt.Errorf("failed to serialize FAT: %v", err)
	}
	fatData := fatBuffer.Bytes()

	// Ghi tuần tự từng block của FAT
	// Mỗi block chỉ có thể chứa (BLOCK_SIZE - 16) bytes dữ liệu thực
	maxDataPerBlock := shared.BLOCK_SIZE - 16
	for i := uint32(0); i < sb.FatBlocks; i++ {
		blockIndex := 1 + i
		start := int64(i) * int64(maxDataPerBlock)
		end := start + int64(maxDataPerBlock)
		if end > int64(len(fatData)) {
			end = int64(len(fatData))
		}

		blockData := fatData[start:end]

		if err := writeBlock(file, blockIndex, blockData, crypter); err != nil {
			return fmt.Errorf("failed to write FAT block %d: %v", blockIndex, err)
		}
	}

	return nil
}

// FindFreeEntry tìm một entry còn trống trong FAT để tạo file mới.
func FindFreeEntry(fat []shared.FileAllocationEntry) (int, error) {
	for i, entry := range fat {
		if !entry.InUse {
			return i, nil
		}
	}
	return -1, fmt.Errorf("FAT is full, cannot create new file")
}

// FindFileByName tìm một file trong FAT dựa vào tên.
func FindFileByName(fat []shared.FileAllocationEntry, name string) (*shared.FileAllocationEntry, int, error) {
	var nameBytes [shared.MAX_FILENAME_LENGTH]byte
	copy(nameBytes[:], name)

	for i, entry := range fat {
		if entry.InUse && entry.Name == nameBytes {
			return &fat[i], i, nil
		}
	}
	return nil, -1, fmt.Errorf("file not found: %s", name)
}

// FindFreeDataBlocks tìm một chuỗi các block dữ liệu còn trống.
// Đây là một phiên bản đơn giản, nó không tối ưu và chỉ tìm từ đầu.
func FindFreeDataBlocks(sb *shared.Superblock, fat []shared.FileAllocationEntry, numBlocksNeeded uint32) (uint32, error) {
	// Tạo một map để đánh dấu các block đã được sử dụng
	usedBlocks := make(map[uint32]bool)

	// Đánh dấu các block của FAT và Superblock là đã dùng
	for i := uint32(0); i < 1+sb.FatBlocks; i++ {
		usedBlocks[i] = true
	}

	// Đánh dấu các block dữ liệu đã được dùng bởi các file khác
	for _, entry := range fat {
		if entry.InUse {
			count, _ := entry.GetBlockSpan()
			for i := uint32(0); i < count; i++ {
				usedBlocks[entry.FirstBlock+i] = true
			}
		}
	}

	// Tìm chuỗi block trống đầu tiên đủ lớn
	startBlock := uint32(1 + sb.FatBlocks) // Bắt đầu tìm từ sau FAT
	for i := startBlock; i < sb.TotalBlocks-numBlocksNeeded; i++ {
		isFreeRun := true
		for j := uint32(0); j < numBlocksNeeded; j++ {
			if usedBlocks[i+j] {
				isFreeRun = false
				i = i + j // Bỏ qua các block đã kiểm tra
				break
			}
		}
		if isFreeRun {
			return i, nil // Tìm thấy!
		}
	}

	return 0, fmt.Errorf("not enough contiguous free space for %d blocks", numBlocksNeeded)
}
